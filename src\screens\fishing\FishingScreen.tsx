import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Dimensions, Button, Pressable } from 'react-native';
import Animated, {
	useSharedValue,
	useAnimatedStyle,
	withTiming,
	withSpring,
	withSequence,
	withDelay,
	interpolate,
	Extrapolation,
	cancelAnimation,
	useDerivedValue,
	useAnimatedReaction,
	SharedValue,
} from 'react-native-reanimated';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { scheduleOnRN } from 'react-native-worklets';

import { Text } from '@/components';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export interface Fish {
	id: string;
	type: 'small' | 'medium' | 'large';
	difficulty: number; // 1-10
	x: number;
	y: number;
	speed: number;
}

export const FishingScreen = () => {
	return (
		<View style={styles.root}>
			<View style={styles.pond}>
				<Animated.View style={styles.fishShadow}>
					<Animated.Image source={require('@/assets/images/tuna.png')} style={styles.fish} />
				</Animated.View>
			</View>
			<View style={styles.fishingRod}>
				<View style={styles.rod}></View>
				<View style={styles.lineContainer}>
					<View style={styles.line}></View>
					<View style={styles.hook}></View>
				</View>
			</View>
			<View style={styles.actions}>
				<Pressable>
					<Text>Thả câu</Text>
				</Pressable>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	root: {
		flex: 1,
		backgroundColor: '#000', // Sky blue
	},
	pond: {
		flex: 1,
		backgroundColor: '#4682B4',
	},
	fishingRod: {
		backgroundColor: '#8B4513',
		height: 150,
	},
	rod: {
		position: 'absolute',
		bottom: 0,
		left: 45,
		width: 10,
		height: 120,
		backgroundColor: '#333', // Saddle brown
		borderRadius: 5,
	},
	lineContainer: {
		position: 'absolute',
		bottom: 110,
		left: 48,
		alignItems: 'center',
	},
	line: {
		width: 2,
		height: 200,
		backgroundColor: '#000',
	},
	hook: {
		width: 8,
		height: 8,
		backgroundColor: '#C0C0C0', // Silver
		borderRadius: 4,
		marginTop: -2,
	},
	fishShadow: {
		position: 'absolute',
		width: 60,
		height: 40,
	},
	fish: {
		width: '100%',
		height: '100%',
		borderRadius: 20,
		opacity: 0.7,
	},
	actions: {
		position: 'absolute',
		bottom: 50,
		left: 20,
		right: 20,
		alignItems: 'center',
	},
});
