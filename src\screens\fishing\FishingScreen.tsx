import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Dimensions, Button, Pressable } from 'react-native';
import Animated, {
	useSharedValue,
	useAnimatedStyle,
	withTiming,
	withSpring,
	withSequence,
	withDelay,
	interpolate,
	Extrapolation,
	cancelAnimation,
	useDerivedValue,
	useAnimatedReaction,
	SharedValue,
} from 'react-native-reanimated';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { scheduleOnRN } from 'react-native-worklets';

import { Text } from '@/components';
import { Fish } from '@/components/fishing/Fish';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

type FishingState = 'idle' | 'casting' | 'fishing' | 'reeling';

export interface Fish {
	id: string;
	type: 'small' | 'medium' | 'large';
	difficulty: number; // 1-10
	x: number;
	y: number;
	speed: number;
}

export const FishingScreen = () => {
	const [state, setState] = useState<FishingState>('idle');

	// Animation values
	const rodRotation = useSharedValue(0);
	const lineLength = useSharedValue(200);
	const hookY = useSharedValue(0);

	const onCast = () => {
		if (state !== 'idle') return;
		setState('casting');

		// Rod casting animation - swing back and forth
		rodRotation.value = withSequence(
			withTiming(-15, { duration: 200 }), // Swing back
			withTiming(0, { duration: 200 }) // Return to normal
		);

		// Line casting animation - extend the line
		lineLength.value = withDelay(
			300, // Start after rod begins forward swing
			withTiming(400, { duration: 800 })
		);

		// Hook animation - move down with the line
		hookY.value = withDelay(300, withTiming(200, { duration: 800 }));
	};

	const onReel = () => {
		if (state !== 'fishing') return;
		setState('reeling');

		// Reel in animation - bring line and hook back up
		lineLength.value = withTiming(200, { duration: 600 });
		hookY.value = withTiming(0, { duration: 600 });

		// Return to idle state after reeling
		setTimeout(() => {
			setState('idle');
		}, 600);
	};

	// Animated styles
	const rodAnimatedStyle = useAnimatedStyle(() => {
		return {
			transform: [
				{ translateX: 5 }, // Move pivot point to bottom of rod
				{ rotate: `${rodRotation.value}deg` },
				{ translateX: -5 },
			],
		};
	});

	const lineAnimatedStyle = useAnimatedStyle(() => {
		return {
			height: lineLength.value,
		};
	});

	const hookAnimatedStyle = useAnimatedStyle(() => {
		return {
			transform: [{ translateY: hookY.value }],
		};
	});

	return (
		<View style={styles.root}>
			<View style={styles.pond}>{state === 'casting' && <Fish />}</View>
			<View style={styles.fishingRod}>
				<Animated.View style={[styles.rod, rodAnimatedStyle]}></Animated.View>
				<View style={styles.lineContainer}>
					<Animated.View style={[styles.line, lineAnimatedStyle]}></Animated.View>
					<Animated.View style={[styles.hook, hookAnimatedStyle]}></Animated.View>
				</View>
			</View>
			<View style={styles.actions}>
				{state === 'idle' && (
					<Pressable style={styles.button} onPress={onCast}>
						<Text style={styles.buttonText}>Thả câu</Text>
					</Pressable>
				)}
				{state === 'fishing' && (
					<Pressable style={styles.button} onPress={onReel}>
						<Text style={styles.buttonText}>Kéo câu</Text>
					</Pressable>
				)}
				{state === 'reeling' && <Text style={styles.statusText}>Đang kéo câu...</Text>}
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	root: {
		flex: 1,
		backgroundColor: '#000', // Sky blue
	},
	pond: {
		flex: 1,
		backgroundColor: '#4682B4',
		justifyContent: 'center',
		alignItems: 'center',
	},
	fishingRod: {
		backgroundColor: '#8B4513',
		height: 150,
	},
	rod: {
		position: 'absolute',
		bottom: 0,
		left: 45,
		width: 10,
		height: 120,
		backgroundColor: '#333', // Saddle brown
		borderRadius: 5,
	},
	lineContainer: {
		position: 'absolute',
		bottom: 110,
		left: 48,
		alignItems: 'center',
	},
	line: {
		width: 2,
		height: 200,
		backgroundColor: '#000',
	},
	hook: {
		width: 8,
		height: 8,
		backgroundColor: '#C0C0C0', // Silver
		borderRadius: 4,
		marginTop: -2,
	},
	fishShadow: {
		position: 'absolute',
		width: 60,
		height: 40,
	},
	fish: {
		width: '100%',
		height: '100%',
		borderRadius: 20,
		opacity: 0.7,
	},
	actions: {
		position: 'absolute',
		bottom: 50,
		left: 20,
		right: 20,
		alignItems: 'center',
	},
	button: {
		backgroundColor: '#4CAF50',
		paddingHorizontal: 20,
		paddingVertical: 12,
		borderRadius: 8,
		elevation: 3,
		shadowColor: '#000',
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.25,
		shadowRadius: 3.84,
	},
	buttonText: {
		color: '#FFFFFF',
		fontSize: 16,
		fontWeight: 'bold',
		textAlign: 'center',
	},
	statusText: {
		color: '#FFFFFF',
		fontSize: 16,
		fontStyle: 'italic',
		textAlign: 'center',
	},
});
