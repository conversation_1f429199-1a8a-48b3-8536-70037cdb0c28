import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

export const unstable_settings = {
	anchor: '(tabs)',
};

export default function RootLayout() {
	return (
		<GestureHandlerRootView>
			<Stack screenOptions={{ headerShown: false, gestureEnabled: false }}>
				<Stack.Screen name="index" />
			</Stack>
			<StatusBar style="dark" backgroundColor="transparent" translucent />
		</GestureHandlerRootView>
	);
}
