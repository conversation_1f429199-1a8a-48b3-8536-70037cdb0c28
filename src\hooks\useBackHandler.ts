import { useCallback } from 'react';
import { BackHandler } from 'react-native';
import { useFocusEffect } from 'expo-router';

/**
 * Hook to handle hardware back button press on Android
 * @param handler Function to execute when back button is pressed
 * @returns Always returns true to indicate the hook is active
 */
export function useBackHandler(handler: () => boolean): boolean {
	useFocusEffect(
		useCallback(() => {
			// Add event listener for back button press
			const backHandler = BackHandler.addEventListener('hardwareBackPress', handler);

			// Clean up event listener on unmount
			return () => {
				backHandler.remove();
			};
		}, [handler])
	);

	return true;
}
