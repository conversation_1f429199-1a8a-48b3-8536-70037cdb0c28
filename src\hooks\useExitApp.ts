import { BackHandler, Platform, ToastAndroid } from 'react-native';

import { useBackHandler } from '@/hooks';

export const useExitApp = () => {
	if (Platform.OS === 'android') {
		let backHandlerClickCount: number = 0;

		const handler = (): boolean => {
			++backHandlerClickCount;
			if (backHandlerClickCount < 2) {
				ToastAndroid.showWithGravityAndOffset(
					'Nhấn back lần nữa để thoát ứng dụng',
					ToastAndroid.SHORT,
					ToastAndroid.BOTTOM,
					25,
					50
				);
			} else {
				BackHandler.exitApp();
			}

			setTimeout(() => {
				backHandlerClickCount = 0;
			}, 2000);
			return true;
		};

		useBackHandler(handler); //eslint-disable-line
	}

	return true;
};
