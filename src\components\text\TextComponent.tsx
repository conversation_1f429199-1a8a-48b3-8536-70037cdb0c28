import { Platform, Text as RNText, TextProps, TextStyle } from 'react-native';

const fonts = [
	'Quicksand',
	'Quicksand-Bold',
	'Quicksand-Light',
	'Quicksand-Regular',
	'Quicksand-Medium',
	'Quicksand-SemiBold',
];

const fontDefault = 'Quicksand';

const Text = (props: TextProps) => {
	const styles = props.style ? getTextStyle(props.style) : undefined;
	return <RNText {...props} allowFontScaling={false} style={styles} />;
};

const getFontName = (style: TextStyle, fontName: string): string => {
	fontName = fontName.replace('-Regular', '').replace('-Bold', '').replace('-Light', '');

	if (typeof style !== 'undefined' && style && style.fontWeight) {
		if (style.fontWeight === 'bold') {
			if (fonts.indexOf(fontName + '-Bold') >= 0) {
				fontName += '-Bold';
			} else {
				fontName += '-Regular';
			}
		} else if (style.fontWeight === 200 || style.fontWeight === 300 || style.fontWeight === 400) {
			if (fonts.indexOf(fontName + '-Light') >= 0) {
				fontName += '-Light';
			} else {
				fontName += '-Regular';
			}
		} else {
			fontName += '-Regular';
		}
	} else {
		fontName += '-Regular';
	}
	if (fonts.indexOf(fontName) < 0) {
		fontName = fontName.replace('-Regular', '');
	}
	return fontName;
};

export const getTextStyle = (origin: TextStyle | any) => {
	const styles = {
		fontFamily: fontDefault,
		fontSize: 13,
	};
	if (Platform.OS === 'android') {
		let fontName = fontDefault;
		let fontWeight = undefined;
		if (typeof origin !== 'undefined') {
			if (Array.isArray(origin)) {
				for (let i in origin) {
					if (typeof origin[i] !== 'undefined' && origin[i]) {
						if (typeof origin[i].fontFamily !== 'undefined') {
							fontName = origin[i].fontFamily;
						}
						if (typeof origin[i].fontWeight !== 'undefined') {
							fontWeight = origin[i].fontWeight;
						}
					}
				}
			} else if (origin && typeof origin.fontFamily !== 'undefined') {
				fontName = origin.fontFamily;
			}
			if (fonts.indexOf(fontName) >= 0) {
				if (Array.isArray(origin)) {
					fontName = getFontName({ fontWeight }, fontName);
					origin.push({
						fontFamily: fontName,
						fontWeight: undefined,
					});
				} else {
					fontName = getFontName(origin, fontName);
					origin = {
						...origin,
						...{ fontFamily: fontName, fontWeight: undefined },
					};
				}
			} else {
				fontName += '-Regular';
			}
		} else {
			fontName += '-Regular';
		}
		styles.fontFamily = fontName;
	}

	if (Array.isArray(origin)) {
		origin = [styles, ...origin];
	} else {
		origin = { ...styles, ...origin };
	}
	return origin;
};

export default Text;
