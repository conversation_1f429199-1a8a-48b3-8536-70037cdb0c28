import { StyleSheet, View } from 'react-native';
import Animated from 'react-native-reanimated';

import { Image } from '@/components';

export const Fish = () => {
	return (
		<View style={styles.box}>
			<Animated.View>
				<Image source={require('@/assets/images/tuna.png')} style={styles.image} />
			</Animated.View>
		</View>
	);
};

const styles = StyleSheet.create({
	box: {
		width: 50,
		height: 50,
		backgroundColor: 'blue',
	},
	image: {
		width: 50,
		height: 50,
	},
});
