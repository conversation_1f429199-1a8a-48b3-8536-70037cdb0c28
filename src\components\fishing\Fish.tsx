import { StyleSheet, View, Dimensions } from 'react-native';
import Animated, {
	useAnimatedStyle,
	useSharedValue,
	withTiming,
	withSequence,
	useDerivedValue,
	Easing,
} from 'react-native-reanimated';

import { Image } from '@/components';
import { useEffect, useCallback } from 'react';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const POND_WIDTH = SCREEN_WIDTH;
const POND_HEIGHT = SCREEN_HEIGHT * 0.7; // Assuming pond takes 70% of screen height
const FISH_SIZE = 50;

export const Fish = () => {
	const x = useSharedValue(Math.random() * (POND_WIDTH - FISH_SIZE));
	const y = useSharedValue(Math.random() * (POND_HEIGHT - FISH_SIZE));
	const prevX = useSharedValue(x.value);
	const prevY = useSharedValue(y.value);
	const rotation = useSharedValue(0);
	const scale = useSharedValue(1);

	// Calculate rotation based on movement direction
	const animatedRotation = useDerivedValue(() => {
		const deltaX = x.value - prevX.value;
		const deltaY = y.value - prevY.value;

		if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
			const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);
			return angle;
		}
		return rotation.value;
	});

	const moveFish = useCallback(() => {
		// Store previous position for rotation calculation
		prevX.value = x.value;
		prevY.value = y.value;

		// Generate new random position within pond bounds
		const newX = Math.random() * (POND_WIDTH - FISH_SIZE);
		const newY = Math.random() * (POND_HEIGHT - FISH_SIZE);

		// Calculate distance for duration (longer distance = longer time)
		const distance = Math.sqrt(Math.pow(newX - x.value, 2) + Math.pow(newY - y.value, 2));
		const duration = Math.max(1500, Math.min(4000, distance * 8)); // 1.5s to 4s based on distance

		// Calculate rotation angle
		const deltaX = newX - x.value;
		const deltaY = newY - y.value;
		const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);

		// Animate to new position with easing
		x.value = withTiming(newX, {
			duration,
			easing: Easing.bezier(0.25, 0.1, 0.25, 1),
		});
		y.value = withTiming(newY, {
			duration,
			easing: Easing.bezier(0.25, 0.1, 0.25, 1),
		});

		// Update rotation smoothly
		rotation.value = withTiming(angle, { duration: 300 });

		// Add subtle scale animation for swimming effect
		scale.value = withSequence(
			withTiming(1.1, { duration: duration / 4 }),
			withTiming(0.9, { duration: duration / 2 }),
			withTiming(1, { duration: duration / 4 })
		);
	}, [x, y, prevX, prevY, rotation, scale]);

	useEffect(() => {
		// Continuous swimming pattern
		const swimInterval = setInterval(
			() => {
				moveFish();
			},
			2000 + Math.random() * 2000
		); // Random interval between 2-4 seconds

		// Start first movement after short delay
		const initialTimer = setTimeout(() => {
			moveFish();
		}, 500);

		return () => {
			clearInterval(swimInterval);
			clearTimeout(initialTimer);
		};
	}, [moveFish]);

	const animatedStyles = useAnimatedStyle(() => {
		return {
			transform: [
				{ translateX: x.value },
				{ translateY: y.value },
				{ rotate: `${rotation.value}deg` },
				{ scale: scale.value },
			],
		};
	});

	return (
		<View style={styles.box}>
			<Animated.View style={animatedStyles}>
				<Image source={require('@/assets/images/tuna.png')} style={styles.image} />
			</Animated.View>
		</View>
	);
};

const styles = StyleSheet.create({
	box: {
		width: 300,
		height: 300,
		backgroundColor: 'blue',
	},
	image: {
		width: 50,
		height: 50,
	},
});
