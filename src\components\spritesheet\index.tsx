import React, { useEffect, useImperativeHandle, useMemo } from 'react';
import { ImageSourcePropType, StyleSheet, ViewStyle } from 'react-native';
import Animated, {
	Easing,
	Extrapolation,
	interpolate,
	useAnimatedStyle,
	useSharedValue,
	withRepeat,
	withTiming,
} from 'react-native-reanimated';
import { scheduleOnRN } from 'react-native-worklets';

export interface PlayConfig {
	animation: string;
	fps?: number;
	loop?: boolean;
	onFinish?: () => void;
}

/**
 * Interface for defining sprite animations
 * @property width - Width of a single frame in pixels
 * @property height - Height of a single frame in pixels
 * @property top - Vertical offset in the sprite sheet
 * @property left - Horizontal offset in the sprite sheet
 * @property frame - Total number of frames in the animation
 * @property rows - Number of rows in the sprite sheet (default: 1)
 * @property columns - Number of columns per row (default: same as frame count)
 */
export interface ISpriteAnimation {
	width: number;
	height: number;
	top: number;
	left: number;
	frame: number;
	rows?: number;
	columns?: number;
}

type Props = {
	source: ImageSourcePropType;
	animations: Record<string, ISpriteAnimation>;
	sheetSize?: {
		width: number;
		height: number;
	};
	width: number;
	style?: ViewStyle | ViewStyle[];
};

export type SpriteSheetRef = {
	play: (option: PlayConfig) => void;
	pause: () => void;
};

/**
 * SpriteSheet component for animating sprite sheets
 *
 * Example usage with multiple rows:
 * ```tsx
 * <SpriteSheet
 *   source={require('@/assets/images/sprites/character.png')}
 *   animations={{
 *     walk: {
 *       width: 64,
 *       height: 64,
 *       top: 0,
 *       left: 0,
 *       frame: 16,
 *       rows: 2,     // Animation spans 2 rows
 *       columns: 8,  // 8 frames per row
 *     },
 *   }}
 *   width={64}
 *   ref={animRef}
 * />
 * ```
 */
export const SpriteSheet = React.forwardRef<SpriteSheetRef, Props>(
	({ source, animations, width, sheetSize, style }, ref) => {
		const transX = useSharedValue(0);
		const transY = useSharedValue(0);

		useImperativeHandle(ref, () => ({
			play(option: PlayConfig) {
				playSheet(option);
			},
			pause() {},
		}));

		useEffect(() => {}, []);

		const interpolationRanges = useMemo(() => {
			let ranges: Record<number, any> = {};
			for (let key in animations) {
				const animation = animations[key];
				const columns = animation.columns || animation.frame;
				const framesPerRow = columns;

				ranges[animation.top] = {
					input: ([] as number[]).concat(
						...Array.from(Array(animation.frame), (_, x) => [x, x + 1])
					),
					outputX: ([] as number[]).concat(
						...Array.from(Array(animation.frame), (_, x) => {
							// Calculate column position (x position in the sprite sheet)
							const columnIndex = x % framesPerRow;
							return [-columnIndex * animation.width, -columnIndex * animation.width];
						})
					),
					outputY: ([] as number[]).concat(
						...Array.from(Array(animation.frame), (_, x) => {
							// Calculate row position (y position in the sprite sheet)
							const rowIndex = Math.floor(x / framesPerRow);
							return [-rowIndex * animation.height, -rowIndex * animation.height];
						})
					),
					width: animation.width,
					height: animation.height,
				};
			}
			return ranges;
		}, [animations]);

		const getAnimation = (animation: string): number => {
			for (let key in animations) {
				if (key === animation) {
					return animations[key].top;
				}
			}
			return 0;
		};

		const playSheet = ({ animation, fps = 16, loop = false, onFinish = () => {} }: PlayConfig) => {
			transY.value = getAnimation(animation);
			if (typeof animations[animation] !== 'undefined') {
				transX.value = 0;
				if (loop) {
					transX.value = withRepeat(
						withTiming(animations[animation].frame, {
							duration: (animations[animation].frame / fps) * 1000,
							easing: Easing.linear,
						}),
						-1,
						false
					);
				} else {
					transX.value = withTiming(
						animations[animation].frame,
						{
							duration: (animations[animation].frame / fps) * 1000,
							easing: Easing.linear,
						},
						() => {
							scheduleOnRN(onFinish);
						}
					);
				}
			}
		};

		const animatedStyles = useAnimatedStyle(() => {
			return {
				transform: [
					{
						translateX: interpolate(
							transX.value,
							interpolationRanges[transY.value].input,
							interpolationRanges[transY.value].outputX,
							{ extrapolateRight: Extrapolation.IDENTITY }
						),
					},
					{
						translateY:
							interpolate(
								transX.value,
								interpolationRanges[transY.value].input,
								interpolationRanges[transY.value].outputY,
								{ extrapolateRight: Extrapolation.IDENTITY }
							) - transY.value,
					},
				],
			};
		}, []);

		const wrapStyle = useAnimatedStyle(() => {
			return {
				width: interpolationRanges[transY.value].width,
				height: interpolationRanges[transY.value].height,
				transform: [{ scale: width / interpolationRanges[transY.value].width }],
			};
		}, [width]);

		return (
			<Animated.View style={[styles.wrap, style, wrapStyle]}>
				<Animated.Image source={source} style={[styles.image, sheetSize, animatedStyles]} />
			</Animated.View>
		);
	}
);

SpriteSheet.displayName = 'SpriteSheet';

const styles = StyleSheet.create({
	wrap: {
		overflow: 'hidden',
		transformOrigin: 'top left',
	},
	image: {},
});
